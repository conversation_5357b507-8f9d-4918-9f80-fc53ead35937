import 'package:io_web_lib/logging.dart';
import 'package:meta/meta.dart';

import '../environment.dart';
import '../has_subscription.dart';
import '../html_root.dart';
import '../releaser.dart';

abstract class CxBase<T extends HTMLElement> with HasSubscription, HtmlRoot<T>, Releaser implements Releasable {
  static final Logger _log = Logger('CxBase', browser: Logger.INFO);

  CxBase({required Releaser releaser}) {
    releaser.addDisposable(this);
  }

  static String _convertToClassName(String className) {
    return className.hyphen();
  }

  void releaseComponent() {} // to override

  /// This method is intended for base-level cleanup only and should not be overridden.
  @override
  @nonVirtual
  void release() {
    releaseComponent();
    releaseSubscriptions();
    releaseAll(); // from Releaser
  }

  T buildComponent();

  @override
  T build() {
    final el = buildComponent();
    el.classList.add(_convertToClassName(runtimeType.toString()));
    return el;
  }
}
