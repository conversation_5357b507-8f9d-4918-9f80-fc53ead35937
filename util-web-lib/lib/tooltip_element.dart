import 'package:io_web_lib/logging.dart';
import 'package:vector_math/vector_math.dart';

import 'environment.dart';
import 'has_subscription.dart';
import 'helper.dart' as helper;
import 'html_root.dart';
import 'releaser.dart';

enum PointyPlacement {
  none,
  topLeft,
  topCenter,
  topRight,
  bottomLeft,
  bottomCenter,
  bottomRight,
  leftTop,
  leftCenter,
  leftBottom,
  rightTop,
  rightCenter,
  rightBottom,
}

class TooltipElement with HasSubscription, HtmlRoot, Releaser implements Releasable {
  static final Logger _log = Logger('TooltipElement', browser: Logger.INFO);

  final String? textContent;
  final Node? htmlContent;
  final List<String> _innerClasses;

  // sizing and placement
  final PointyPlacement _pointyPlacement;
  final Vector2? absolute;
  final String? nudgeX;
  final String? nudgeY;
  final double? maxWidth;

  // Auto-hover functionality
  final HTMLElement? _targetElement;
  bool _autoHover = false;

  TooltipElement(
    this._pointyPlacement, {
    this.textContent,
    this.htmlContent,
    this.maxWidth,
    this.absolute,
    this.nudgeX,
    this.nudgeY,
    List<String> innerClasses = const [],
    HTMLElement? targetElement,
  }) : _innerClasses = innerClasses,
       _targetElement = targetElement;

  /// Factory method to create a tooltip with automatic hover handling
  static TooltipElement withAutoHover({
    required HTMLElement targetElement,
    required PointyPlacement placement,
    String? textContent,
    Node? htmlContent,
    double? maxWidth,
    String? nudgeX,
    String? nudgeY,
    List<String> innerClasses = const [],
  }) {
    final tooltip = TooltipElement(
      placement,
      textContent: textContent,
      htmlContent: htmlContent,
      maxWidth: maxWidth,
      nudgeX: nudgeX,
      nudgeY: nudgeY,
      innerClasses: innerClasses,
      targetElement: targetElement,
    );
    tooltip.enableAutoHover();
    return tooltip;
  }

  /// Enables automatic hover handling for the tooltip
  void enableAutoHover() {
    if (_targetElement == null || _autoHover) return;

    _autoHover = true;

    // Listen to mouse enter on target element
    autoUnsubscribe(_targetElement.onMouseEnter.listen((_) {
      // Cancel any pending hide operation
      helper.terminateDelayedCall(channel: 'hideTooltip', executeCallback: false);
      _showTooltip();
    }));

    // Listen to mouse leave on target element
    autoUnsubscribe(_targetElement.onMouseLeave.listen((_) {
      _hideTooltip();
    }));
  }

  void _showTooltip() {
    if (_targetElement == null) return;

    // Only show if not already visible
    if (cachedRoot?.parentNode != null) return;

    // Calculate position relative to target element
    final targetRect = _targetElement.getBoundingClientRect();
    Vector2 position;

    switch (_pointyPlacement) {
      case PointyPlacement.bottomCenter:
        position = Vector2(
          targetRect.left + targetRect.width / 2, targetRect.top - 4
        );
        break;
      case PointyPlacement.topCenter:
        position = Vector2(
          targetRect.left + targetRect.width / 2, targetRect.bottom + 4
        );
        break;
      default:
        _log.warning(() => 'TooltipElement._showTooltip: Unsupported placement: ${_pointyPlacement.name}');
        position = Vector2(
          targetRect.left + targetRect.width / 2,
          targetRect.bottom + 4
        );
    }

    // Ensure tooltip doesn't go off-screen
    final adjustedPosition = _adjustPositionForViewport(position);
    final tooltipElement = root;
    tooltipElement.style.zIndex = '1000';
    Environment.document.body?.appendChild(tooltipElement);

    if (_autoHover) {
      autoUnsubscribe(tooltipElement.onMouseEnter.listen((_) {
        helper.terminateDelayedCall(channel: 'hideTooltip', executeCallback: false);
      }));

      autoUnsubscribe(tooltipElement.onMouseLeave.listen((_) {
        _hideTooltip();
      }));
    }

    updateAbsoluteLocation(adjustedPosition);
  }

  Vector2 _adjustPositionForViewport(Vector2 position) {
    final viewport = Environment.window.innerWidth;
    final viewportHeight = Environment.window.innerHeight;

    // Get tooltip dimensions (estimate if not available)
    final tooltipWidth = maxWidth ?? 200;
    const tooltipHeight = 40; // Estimated height

    var adjustedX = position.x;
    var adjustedY = position.y;

    // Adjust horizontal position
    if (position.x + tooltipWidth / 2 > viewport) {
      adjustedX = viewport - tooltipWidth / 2 - 10;
    } else if (position.x - tooltipWidth / 2 < 0) {
      adjustedX = tooltipWidth / 2 + 10;
    }

    // Adjust vertical position
    if (position.y + tooltipHeight > viewportHeight) {
      adjustedY = position.y - tooltipHeight - 16;
    } else if (position.y < 0) {
      adjustedY = 10;
    }
    return Vector2(adjustedX, adjustedY);
  }

  void _hideTooltip() {
    // Use a small delay to prevent flickering when mouse moves quickly
    helper.delayedCall(0.05, () {
      hideAndRemoveRoot();
    }, channel: 'hideTooltip');
  }

  @override
  void release() {
    helper.terminateDelayedCall(channel: 'copied', executeCallback: false);
    helper.terminateDelayedCall(channel: 'removeRoot', executeCallback: false);
    helper.terminateDelayedCall(channel: 'hideTooltip', executeCallback: false);
    removeRoot();
    releaseAll();
  }

  void hideAndRemoveRoot() {
    final div = cachedRoot;
    if (div == null) return;

    final rect = div.getBoundingClientRect().toAabb2();
    if (rect.min.isZero()) {
      removeRoot();
      return;
    }

    div.classList.remove('absolute');
    div.classList.add('fixed');
    div.style.setProperty('--viewport-left', '${rect.min.x}px');
    div.style.setProperty('--viewport-top', '${rect.min.y}px');
    helper.triggerCssAnimation(div, 'active', removeClass: true);
    Environment.document.body?.appendChild(div);
    helper.delayedCall(0.5, () => removeRoot(), channel: 'removeRoot');
  }

  @override
  HTMLElement build() {
    final content = HTMLDivElement()..classList.addAll(['content', ..._innerClasses]);
    if (textContent != null) {
      content.textContent = textContent;
    }
    if (htmlContent != null) {
      content.appendNotNull(htmlContent);
    }

    final div = HTMLDivElement()
      ..classList.addAll(['tooltip', 'pointy-${_pointyPlacement.name}'])
      ..appendChild(
        HTMLDivElement()
          ..classList.add('tooltip-border')
          ..appendChild(content),
      );

    if (absolute != null) {
      div.classList.add('absolute');
      div.style.setProperty('--absolute-x', '${absolute!.x}px');
      div.style.setProperty('--absolute-y', '${absolute!.y}px');
    }

    if (nudgeX != null) {
      div.style.setProperty('--nudge-x', nudgeX!);
    }

    if (nudgeY != null) {
      div.style.setProperty('--nudge-y', nudgeY!);
    }

    if (maxWidth != null) {
      div.style.setProperty('--max-width', '${maxWidth}px');
    }

    helper.triggerCssAnimation(div, 'active');

    return div;
  }

  void updateAbsoluteLocation(Vector2? location) {
    helper.delayedCall(0.01, () {
      if (location == null) return;

      final div = cachedRoot;
      if (div == null) return;

      div.classList.add('absolute');
      div.style.setProperty('--absolute-x', '${location.x}px');
      div.style.setProperty('--absolute-y', '${location.y}px');
    }, channel: 'updateAbsoluteLocation');
  }

  void updateNudgeLocation(String? nudgeX, String? nudgeY) {
    helper.delayedCall(0.01, () {
      final div = cachedRoot;
      if (div == null) return;

      if (nudgeX != null) {
        div.style.setProperty('--nudge-x', nudgeX);
      }

      if (nudgeY != null) {
        div.style.setProperty('--nudge-y', nudgeY);
      }
    }, channel: 'updateNudgeLocation');
  }

  void updateInnerText(String text) {
    final div = cachedRoot;
    if (div == null) return;

    final content = div.querySelector('.content');
    if (content == null) return;

    content.textContent = text;
  }
}
