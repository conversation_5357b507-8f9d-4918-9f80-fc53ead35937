import 'package:test/test.dart';
import 'package:util_web_lib/releaser.dart';
import 'package:util_web_lib/ui-kit/cx_tooltip.dart';
import 'package:vector_math/vector_math.dart';

void main() {
  group('CxTooltip', () {
    late Releaser releaser;

    setUp(() {
      releaser = Releaser();
    });

    tearDown(() {
      releaser.releaseAll();
    });

    test('should create tooltip with basic configuration', () {
      final tooltip = CxTooltip(
        releaser: releaser,
        placement: CxTooltipPlacement.topCenter,
        textContent: 'Test tooltip',
      );

      final root = tooltip.root;
      expect(root.classList.contains('tooltip'), isTrue);
      expect(root.classList.contains('pointy-topCenter'), isTrue);
      expect(root.querySelector('.content')?.textContent, equals('Test tooltip'));
    });

    test('should support all placement positions', () {
      final placements = [
        CxTooltipPlacement.topLeft,
        CxTooltipPlacement.topCenter,
        CxTooltipPlacement.topRight,
        CxTooltipPlacement.bottomLeft,
        CxTooltipPlacement.bottomCenter,
        CxTooltipPlacement.bottomRight,
        CxTooltipPlacement.leftTop,
        CxTooltipPlacement.leftCenter,
        CxTooltipPlacement.leftBottom,
        CxTooltipPlacement.rightTop,
        CxTooltipPlacement.rightCenter,
        CxTooltipPlacement.rightBottom,
      ];

      for (final placement in placements) {
        final tooltip = CxTooltip(
          releaser: releaser,
          placement: placement,
          textContent: 'Test',
        );

        expect(tooltip.root.classList.contains('pointy-${placement.name}'), isTrue);
      }
    });

    test('should apply custom configuration', () {
      const config = CxTooltipConfig(
        defaultWidth: 300,
        defaultHeight: 50,
        viewportMargin: 20,
        targetOffset: 8,
        zIndex: 2000,
      );

      final tooltip = CxTooltip(
        releaser: releaser,
        placement: CxTooltipPlacement.topCenter,
        textContent: 'Test',
        config: config,
      );

      // Configuration is applied internally, we can't directly test it
      // but we can verify the tooltip was created successfully
      expect(tooltip.root.classList.contains('tooltip'), isTrue);
    });

    test('should support absolute positioning', () {
      final tooltip = CxTooltip(
        releaser: releaser,
        placement: CxTooltipPlacement.topCenter,
        textContent: 'Test',
        absolute: Vector2(100, 200),
      );

      final root = tooltip.root;
      expect(root.classList.contains('absolute'), isTrue);
      expect(root.style.getPropertyValue('--absolute-x'), equals('100.0px'));
      expect(root.style.getPropertyValue('--absolute-y'), equals('200.0px'));
    });

    test('should support nudge positioning', () {
      final tooltip = CxTooltip(
        releaser: releaser,
        placement: CxTooltipPlacement.topCenter,
        textContent: 'Test',
        nudgeX: '10px',
        nudgeY: '5px',
      );

      final root = tooltip.root;
      expect(root.style.getPropertyValue('--nudge-x'), equals('10px'));
      expect(root.style.getPropertyValue('--nudge-y'), equals('5px'));
    });

    test('should support max width', () {
      final tooltip = CxTooltip(
        releaser: releaser,
        placement: CxTooltipPlacement.topCenter,
        textContent: 'Test',
        maxWidth: 250,
      );

      final root = tooltip.root;
      expect(root.style.getPropertyValue('--max-width'), equals('250.0px'));
    });

    test('should support inner classes', () {
      final tooltip = CxTooltip(
        releaser: releaser,
        placement: CxTooltipPlacement.topCenter,
        textContent: 'Test',
        innerClasses: ['custom-class', 'another-class'],
      );

      final content = tooltip.root.querySelector('.content');
      expect(content?.classList.contains('custom-class'), isTrue);
      expect(content?.classList.contains('another-class'), isTrue);
    });

    test('should convert from old PointyPlacement', () {
      // Test a few key conversions
      expect(
        CxTooltip.fromPointyPlacement(PointyPlacement.topCenter),
        equals(CxTooltipPlacement.topCenter),
      );
      expect(
        CxTooltip.fromPointyPlacement(PointyPlacement.bottomLeft),
        equals(CxTooltipPlacement.bottomLeft),
      );
      expect(
        CxTooltip.fromPointyPlacement(PointyPlacement.rightCenter),
        equals(CxTooltipPlacement.rightCenter),
      );
    });

    test('should update inner text', () {
      final tooltip = CxTooltip(
        releaser: releaser,
        placement: CxTooltipPlacement.topCenter,
        textContent: 'Original text',
      );

      tooltip.updateInnerText('Updated text');
      
      final content = tooltip.root.querySelector('.content');
      expect(content?.textContent, equals('Updated text'));
    });
  });
}
