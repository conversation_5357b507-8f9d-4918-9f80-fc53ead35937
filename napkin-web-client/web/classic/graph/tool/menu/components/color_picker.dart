import 'package:analytics_web_lib/analytics_web_lib.dart';
import 'package:io_web_lib/logging.dart';
import 'package:util_web_lib/environment.dart';
import 'package:util_web_lib/eye_dropper.dart';
import 'package:util_web_lib/has_subscription.dart';
import 'package:util_web_lib/helper.dart' as helper;
import 'package:util_web_lib/html_root.dart';
import 'package:util_web_lib/privateer.dart';
import 'package:util_web_lib/ui-kit/cx_tooltip.dart';

import '../../../ecs/item/services/service_brush.dart';
import '../../../graph.dart';
import '../item_menu_provider.dart';
import './color_slider.dart';

typedef SetColorFunc = void Function(String color);
typedef PreviewColorFunc = void Function(String hexColor);
typedef ClearPreviewFunc = void Function();
typedef RemoveColorFunc = void Function(String color);
typedef CloseFunc = void Function();

final class ColorPicker with HasSubscription, HtmlRoot, Privateer<ColorPickerPrivateer> {
  static final _log = Logger('ColorPicker');

  final _commitColorButton = HTMLDivElement()..classList.addAll(['commit_color', 'icon', 'active']);

  late final ColorSlider _colorSlider;
  late final ColorInputBox _colorInputBox;
  late final EyeDropper _eyeDropper;
  late final UserSelectedColors _userSelectedColors;

  late HTMLDivElement divider;

  String hexColor;

  final Graph graph;
  final CloseFunc closeFunc;
  final SetColorFunc setColorFunc;

  ColorPicker(
    this.graph,
    this.hexColor, {
    required bool fillDisplay,
    required this.closeFunc,
    required this.setColorFunc,
    required PreviewColorFunc previewColorFunc,
    required ClearPreviewFunc clearPreviewColorFunc,
  }) {
    _colorSlider = ColorSlider(hexColor, onColorPreview: _onColorPickerPreview, onColorChanged: _onColorPickerChanged);
    _colorInputBox = ColorInputBox(hexColor, onInputChanged: _onColorInputChanged);
    _eyeDropper = EyeDropper();
    _userSelectedColors = UserSelectedColors(
      graph,
      fillDisplay: fillDisplay,
      removeColorFunc: _removeCustomColor,
      setColorFunc: _setColorFunc,
      previewColorFunc: previewColorFunc,
      clearPreviewColorFunc: clearPreviewColorFunc,
    );
  }

  void release() {
    releaseSubscriptions();
    _colorSlider.release();
    _userSelectedColors.release();
    _colorInputBox.release();
  }

  @override
  HTMLElement build() {
    cancelBuildSubscriptions();

    // Header Element
    final closeButton = HTMLDivElement()..classList.addAll(['icon', 'close', 'cross']);
    autoUnsubscribeBuild(closeButton.onClick.listen((_) => closeFunc()));

    final header = HTMLDivElement()
      ..classList.add('header')
      ..appendChild(
        HTMLDivElement()
          ..textContent = 'CUSTOM COLORS'
          ..classList.add('title'),
      )
      ..appendChild(closeButton);

    // Eye dropper
    final eyeDropperEnabled = _eyeDropper.isAvailable();
    final eyeDropperElement = HTMLDivElement()..classList.addAll(['eye_dropper', 'icon', if (!eyeDropperEnabled) 'disabled']);
    final eyeDropperContainer = HTMLDivElement()
      ..classList.addAll(['eye_dropper_container'])
      ..appendChild(eyeDropperElement);
    if (eyeDropperEnabled) {
      autoUnsubscribeBuild(
        eyeDropperElement.onClick.listen((MouseEvent event) {
          event.preventDefault();
          event.stopPropagation();
          _openEyedropper();
        }),
      );
    }

    autoUnsubscribeBuild(_commitColorButton.onClick.listen((_) => _addCustomColor(_colorInputBox.color)));
    final colorInputContainer = HTMLDivElement()
      ..classList.add('color_input_container')
      ..appendAll([eyeDropperContainer, _colorInputBox.root, _commitColorButton]);

    //Divider
    divider = HTMLDivElement()..classList.add('divider');

    // call the method here for userSelectedColors
    toggleUserSelectedColors(_userSelectedColors.isEmpty);

    return HTMLDivElement()
      ..classList.addAll(['color-picker', 'dom_capture_all'])
      ..appendAll([header, _colorSlider.root, colorInputContainer, divider, _userSelectedColors.root]);
  }

  void _onColorPickerChanged(String color) {
    _commitColorButton.classList.toggle('active', true);
    _colorInputBox.color = color;
  }

  void _onColorPickerPreview(String color) {
    _commitColorButton.classList.toggle('active', true);
    _colorInputBox.color = color;
  }

  void _onColorInputChanged(String color, bool isValid) {
    _commitColorButton.classList.toggle('active', isValid);
    if (isValid) _colorSlider.color = color;
  }

  void _onEyeDropperColorPicked(String color) {
    _commitColorButton.classList.toggle('active', true);
    _colorInputBox.color = color;
    _colorSlider.color = color;
  }

  void toggleUserSelectedColors(bool hide) {
    _userSelectedColors.root.style.display = hide ? 'none' : '';
    divider.style.display = hide ? 'none' : '';
  }

  void _addCustomColor(String rgb) {
    // add # if not and capitalize
    if (!rgb.startsWith('#')) rgb = '#$rgb';
    rgb = rgb.toUpperCase();

    _userSelectedColors.commitColor(rgb);
    toggleUserSelectedColors(_userSelectedColors.isEmpty);
    ItemMenuProvider.sendAnalyticsEvent('color', 'create_custom_color', {'value': '#$rgb'});
  }

  void _setColorFunc(String color) {
    setColorFunc(color);
    // update the current color
    _colorSlider.color = color;
    _colorInputBox.color = color;
  }

  void _removeCustomColor(String rgb) {
    toggleUserSelectedColors(_userSelectedColors.isEmpty);
  }

  void _openEyedropper() {
    if (!_eyeDropper.isAvailable()) return;

    _eyeDropper
        .open()
        .then((color) {
          if (color == null) return;
          _onEyeDropperColorPicked(color);
        })
        .catchError((error) {
          _log.warning(() => 'Eyedropper failed: $error');
        });
  }

  @override
  ColorPickerPrivateer get createPrivateer => ColorPickerPrivateer(this);
}

class UserSelectedColors with HtmlRoot, HasSubscription {
  final Graph graph;
  final List<String> _colors;
  String? _newColor;

  late HTMLDivElement selectedColorsContainer;

  final RemoveColorFunc removeColorFunc;
  final SetColorFunc setColorFunc;
  final PreviewColorFunc previewColorFunc;
  final ClearPreviewFunc clearPreviewColorFunc;

  bool get isEmpty => _colors.isEmpty;

  final bool fillDisplay;

  UserSelectedColors(
    this.graph, {
    required this.fillDisplay,
    required this.removeColorFunc,
    required this.setColorFunc,
    required this.previewColorFunc,
    required this.clearPreviewColorFunc,
  }) : _colors = serviceCustomColorsGet(graph);

  void release() {
    releaseSubscriptions();
    _colors.clear();
  }

  @override
  HTMLElement build() {
    cancelBuildSubscriptions();

    selectedColorsContainer = HTMLDivElement()..classList.add('user_selected_colors_container');
    for (final color in _colors) {
      final removeColorButton = HTMLDivElement()..classList.addAll(['remove_custom_color', 'icon']);
      autoUnsubscribeBuild(removeColorButton.onClick.listen((e) => _removeColor(e, color)));

      final colorCircle = HTMLDivElement()
        ..style.setProperty('--color', color)
        ..classList.addAll(['color_box']);

      autoUnsubscribeBuild(colorCircle.onMouseEnter.listen((_) => previewColorFunc(color)));
      autoUnsubscribeBuild(colorCircle.onMouseLeave.listen((_) => clearPreviewColorFunc()));
      autoUnsubscribeBuild(colorCircle.onClick.listen((e) => _setColor(e, color)));

      if (_newColor == color) {
        helper.triggerCssAnimation(colorCircle, 'bump-animation');
        _newColor = null;
      }

      selectedColorsContainer.appendChild(
        HTMLDivElement()
          ..classList.add('user_selected_color')
          ..appendAll([colorCircle, removeColorButton]),
      );
    }
    selectedColorsContainer.classList.add(fillDisplay ? 'fill' : 'stroke');

    selectedColorsContainer.appendChild(_buildStyleButton());

    return selectedColorsContainer;
  }

  HTMLElement _buildStyleButton() {
    cancelSubscriptions('style_button');

    final useExploreStylesButton = Flags().variation('theme-editor-explorer-style', false);
    final createStyleButton = HTMLDivElement()..classList.addAll(['create_style_icon', 'icon', 'theme', 'inverted']);
    autoUnsubscribe(createStyleButton.onClick.listen(useExploreStylesButton ? _openThemeSelector : _openThemeEditor), 'style_button');

    return HTMLDivElement()
      ..classList.addAll(['tooltip_holder', 'tooltip_on_hover_only'])
      ..appendAll([
        createStyleButton,
        TooltipElement(
          PointyPlacement.bottomCenter,
          textContent: useExploreStylesButton ? 'Explore Styles' : 'Create My Style',
          nudgeY: '4px',
          innerClasses: ['button', 'create_style', 'inverted', 'small', if (!useExploreStylesButton) 'pro'],
        ).root..classList.addAll(['create_style_tooltip', 'inverted']),
      ]);
  }

  void _openThemeEditor(_) {
    graph.openThemeEditor(newThemeSource: 'color_picker', colors: _colors);
  }

  void _openThemeSelector(_) {
    graph.openThemeSelector(source: 'color_picker');
  }

  void _setColor(MouseEvent event, String color) {
    setColorFunc(color);
    event.stopPropagation();
  }

  void _removeColor(MouseEvent event, String color) {
    event.stopPropagation();
    // Find the element corresponding to the color
    final colorIndex = _colors.indexOf(color);
    if (colorIndex == -1) return; // Color not found

    final colorElements = Environment.document.querySelectorAll('.user_selected_color');
    final elementToRemove = colorElements.toList().elementAt(colorIndex) as HTMLDivElement?;

    // Apply fade-out transition
    elementToRemove?.classList.add('removing');

    // After the transition, remove the color and rebuild the UI
    Future.delayed(const Duration(milliseconds: 500), () => removeColorInternal(color, elementToRemove));
  }

  void commitColor(String newColor) {
    final updatedColors = serviceCustomColorsStore(graph, newColor).$1;
    _newColor = newColor;
    _colors.clearAddAll(updatedColors);
    rebuild();
  }

  void removeColorInternal(String color, HTMLDivElement? elementToRemove) {
    _colors.remove(color);
    serviceCustomColorsRemove(graph, color);
    elementToRemove?.remove(); // Remove the element from the DOM
    removeColorFunc(color);
    rebuild(); // Rebuild the root to reflect changes
  }
}

class ColorInputBox with HtmlRoot, HasSubscription {
  final HTMLInputElement _inputElement = HTMLInputElement()..classList.add('color_input');
  final HTMLDivElement _colorDisplay = HTMLDivElement()..classList.add('color_display');

  String get color => '#${_inputElement.value}';

  final void Function(String color, bool isValid) onInputChanged;

  ColorInputBox(String initialColor, {required this.onInputChanged}) {
    // Set up event listener to handle input
    _inputElement.placeholder = 'Enter';
    autoUnsubscribe(_inputElement.onChange.listen((event) => _onInputChanged()));
    autoUnsubscribe(_inputElement.onInput.listen((event) => _onInputChanged()));

    color = initialColor;
  }

  void release() {
    releaseSubscriptions();
  }

  @override
  HTMLElement build() {
    final container = HTMLDivElement()..classList.add('color_input_box');
    container.appendChild(_inputElement);
    container.appendChild(_colorDisplay);
    return container;
  }

  set color(String newColor) {
    newColor = _sanitize(newColor);
    if (newColor == color) return; // ignore if the color is the same

    // ignore bad values coming form the the outside
    if (!_isValidHex(newColor)) return;

    _inputElement.value = newColor;
    _colorDisplay.style.backgroundColor = '#$newColor';
  }

  void _onInputChanged() {
    final newColor = _sanitize(_inputElement.value);

    if (!_isValidHex(newColor)) {
      onInputChanged(newColor, false);
      return;
    }

    onInputChanged(newColor, true);

    _inputElement.value = newColor; // reset with the sanitized value
    _colorDisplay.style.backgroundColor = '#$newColor';
  }

  // Helper function to validate if the input is a valid hex color
  bool _isValidHex(String hex) {
    // Hex color validation regex (accepts both 3 and 6 character hex, with '#')
    final hexRegExp = RegExp(r'^([A-F0-9]{6}|[A-F0-9]{3})$', caseSensitive: false);
    return hexRegExp.hasMatch(hex);
  }

  static String _sanitize(String color) {
    color = color.trim().toUpperCase();
    // opportunistic conversion of standard color names
    color = _standardColorConversion(color) ?? color;
    if (color.startsWith('#')) color = color.substring(1);
    return color;
  }

  static String? _standardColorConversion(String color) => switch (color) {
    'ALICEBLUE' => '#F0F8FF',
    'ANTIQUEWHITE' => '#FAEBD7',
    'AQUA' => '#00FFFF',
    'AQUAMARINE' => '#7FFFD4',
    'AZURE' => '#F0FFFF',
    'BEIGE' => '#F5F5DC',
    'BISQUE' => '#FFE4C4',
    'BLACK' => '#000000',
    'BLANCHEDALMOND' => '#FFEBCD',
    'BLUE' => '#0000FF',
    'BLUEVIOLET' => '#8A2BE2',
    'BROWN' => '#A52A2A',
    'BURLYWOOD' => '#DEB887',
    'CADETBLUE' => '#5F9EA0',
    'CHARTREUSE' => '#7FFF00',
    'CHOCOLATE' => '#D2691E',
    'CORAL' => '#FF7F50',
    'CORNFLOWERBLUE' => '#6495ED',
    'CORNSILK' => '#FFF8DC',
    'CRIMSON' => '#DC143C',
    'CYAN' => '#00FFFF',
    'DARKBLUE' => '#00008B',
    'DARKCYAN' => '#008B8B',
    'DARKGOLDENROD' => '#B8860B',
    'DARKGRAY' => '#A9A9A9',
    'DARKGREEN' => '#006400',
    'DARKKHAKI' => '#BDB76B',
    'GOLD' => '#FFD700',
    'GOLDENROD' => '#DAA520',
    'GRAY' => '#808080',
    'GREEN' => '#008000',
    'GREENYELLOW' => '#ADFF2F',
    'HONEYDEW' => '#F0FFF0',
    'HOTPINK' => '#FF69B4',
    'INDIANRED' => '#CD5C5C',
    'INDIGO' => '#4B0082',
    'IVORY' => '#FFFFF0',
    'KHAKI' => '#F0E68C',
    'LAVENDER' => '#E6E6FA',
    'LAVENDERBLUSH' => '#FFF0F5',
    'LAWNGREEN' => '#7CFC00',
    'LEMONCHIFFON' => '#FFFACD',
    'LIGHTBLUE' => '#ADD8E6',
    'LIGHTCORAL' => '#F08080',
    'LIGHTCYAN' => '#E0FFFF',
    'LIGHTGOLDENRODYELLOW' => '#FAFAD2',
    'LIGHTGREEN' => '#90EE90',
    'LIGHTGRAY' => '#D3D3D3',
    'LIGHTPINK' => '#FFB6C1',
    'LIGHTSALMON' => '#FFA07A',
    'LIGHTSEAGREEN' => '#20B2AA',
    'LIGHTSKYBLUE' => '#87CEFA',
    'LIGHTSLATEGRAY' => '#778899',
    'LIGHTSTEELBLUE' => '#B0C4DE',
    'LIGHTYELLOW' => '#FFFFE0',
    'LIME' => '#00FF00',
    'LIMEGREEN' => '#32CD32',
    'LINEN' => '#FAF0E6',
    'MAGENTA' => '#FF00FF',
    'MAROON' => '#800000',
    'MEDIUMAQUAMARINE' => '#66CDAA',
    'MEDIUMBLUE' => '#0000CD',
    'MEDIUMORCHID' => '#BA55D3',
    'MEDIUMPURPLE' => '#9370DB',
    'MEDIUMSEAGREEN' => '#3CB371',
    'MEDIUMSLATEBLUE' => '#7B68EE',
    'MEDIUMSPRINGGREEN' => '#00FA9A',
    'MEDIUMTURQUOISE' => '#48D1CC',
    'MEDIUMVIOLETRED' => '#C71585',
    'MIDNIGHTBLUE' => '#191970',
    'MINTCREAM' => '#F5FFFA',
    'MISTYROSE' => '#FFE4E1',
    'MOCCASIN' => '#FFE4B5',
    'NAVY' => '#000080',
    'OLDLACE' => '#FDF5E6',
    'OLIVE' => '#808000',
    'OLIVEDRAB' => '#6B8E23',
    'ORANGE' => '#FFA500',
    'ORANGERED' => '#FF4500',
    'ORCHID' => '#DA70D6',
    'PALEGOLDENROD' => '#EEE8AA',
    'PALEGREEN' => '#98FB98',
    'PALETURQUOISE' => '#AFEEEE',
    'PALEVIOLETRED' => '#DB7093',
    'PAPAYAWHIP' => '#FFEFD5',
    'PEACHPUFF' => '#FFDAB9',
    'PERU' => '#CD853F',
    'PINK' => '#FFC0CB',
    'PLUM' => '#DDA0DD',
    'POWDERBLUE' => '#B0E0E6',
    'PURPLE' => '#800080',
    'RED' => '#FF0000',
    'ROSYBROWN' => '#BC8F8F',
    'ROYALBLUE' => '#4169E1',
    'SALMON' => '#FA8072',
    'SANDYBROWN' => '#F4A460',
    'SEAGREEN' => '#2E8B57',
    'SEASHELL' => '#FFF5EE',
    'SIENNA' => '#A0522D',
    'SILVER' => '#C0C0C0',
    'SKYBLUE' => '#87CEEB',
    'SLATEBLUE' => '#6A5ACD',
    'SLATEGRAY' => '#708090',
    'SNOW' => '#FFFAFA',
    'SPRINGGREEN' => '#00FF7F',
    'STEELBLUE' => '#4682B4',
    'TAN' => '#D2B48C',
    'TEAL' => '#008080',
    'THISTLE' => '#D8BFD8',
    'TOMATO' => '#FF6347',
    'TURQUOISE' => '#40E0D0',
    'VIOLET' => '#EE82EE',
    'WHEAT' => '#F5DEB3',
    'WHITE' => '#FFFFFF',
    'WHITESMOKE' => '#F5F5F5',
    'YELLOW' => '#FFFF00',
    'YELLOWGREEN' => '#9ACD32',
    _ => null,
  };
}

class ColorPickerPrivateer {
  final ColorPicker _colorPicker;
  ColorPickerPrivateer(this._colorPicker);

  ColorSlider get colorSlider => _colorPicker._colorSlider;
  ColorInputBox get colorInputBox => _colorPicker._colorInputBox;
  EyeDropper get eyeDropper => _colorPicker._eyeDropper;
  UserSelectedColors get userSelectedColors => _colorPicker._userSelectedColors;

  HTMLElement get commitColorButton => _colorPicker._commitColorButton;
  List<String> get userSelectedColorsList => _colorPicker._userSelectedColors._colors;
}
