import 'dart:async';
import 'dart:math';

import 'package:analytics_web_lib/analytics_web_lib.dart';
import 'package:io_web_lib/logging.dart';
import 'package:util_web_lib/browser.dart' as browser;
import 'package:util_web_lib/environment.dart';
import 'package:util_web_lib/function.dart';
import 'package:util_web_lib/has_subscription.dart';
import 'package:util_web_lib/html_root.dart';
import 'package:util_web_lib/privateer.dart';
import 'package:util_web_lib/ui-kit/cx_dropdown.dart';

import '../../app/context.dart';
import '../app_root/model.dart';
import '../dialogs/workspace/workspace_dropdown.dart';
import '../dialogs/workspace/workspace_welcome.dart';
import '../page/page_loader_utils.dart';
import '../page/page_model.dart';
import '../user/user_config_workspace.dart';
import 'add_page_element.dart';
import 'library_item.dart';
import 'library_tab.dart';

abstract class LibraryHost {
  Context get context;

  set uploadedFile(dynamic v);

  Future<void> goToCreatePage();
  Future<PageModel?> goToPage(PageId? pageId, {required ParametersMap parameters});
  PageModel? get currentPageModel;

  Stream<PageModel> get onPageCreated;
  Future<PageModel?> createPage({required bool navigateToPage, String? workflow, NapkinCreationData? creationData});
  Future<void> onPageDuplicated(String pageId, String pageTitle);

  void downloadAsPdf({String? id, String? title}) {}

  void closeLibrary();
  void updateLibraryOverlapping();

  bool get isNightMode;
}

class LibraryPanel with HtmlRoot, HasSubscription, Privateer<LibraryPanelPrivateer> implements LibraryTabHost {
  static final Logger _log = Logger('LibraryPanel');

  @override
  Context get context => host.context;

  @override
  bool get isNightMode => host.isNightMode;

  final LibraryHost host;

  static const overlappingThreshold = 1548;
  static const String key = 'library';

  LibraryTab? _myNapkinsTab;
  LibraryTab? _teamSpaceTab;
  LibraryTab? _allRecentTab;
  LibraryTab? _selectedTab;

  bool get supportPageCreation => AddPageElement.supportPageCreation;
  final AddPageElement? _addPageElement;

  final Element _libraryElement;
  Element? _tabContentHolder;
  Element? _teamSpaceSelector;

  Completer? _isLoadingDataCompleter;
  Future? get loadingFuture => _isLoadingDataCompleter?.future;

  bool _overlapping = false;
  bool get overlapping => _overlapping;

  bool _isShowing = false;
  bool get isShowing => _isShowing;

  final StreamController<bool> _onIsShowingChangedController = StreamController<bool>.broadcast();
  Stream<bool> get onIsShowingChanged => _onIsShowingChangedController.stream;

  final List<StreamSubscription> _buildSubscriptions = [];

  @override
  LibraryPanelPrivateer get createPrivateer => LibraryPanelPrivateer._(this);

  LibraryPanel(this.host, {bool showByDefault = true})
    : _libraryElement = HTMLDivElement()..classList.add('library'),
      _addPageElement = AddPageElement.tryCreate(host) {
    _myNapkinsTab = LibraryTab(this, 'My Napkins', LibraryCollectionType.authoredByMe);
    _allRecentTab = LibraryTab(this, 'Recent', LibraryCollectionType.allRecent);

    autoUnsubscribe(
      context.workSpaceManager.onWorkspaceDataChanged.listen((_) {
        _onWorkspacesUpdated();
      }),
    );

    // Preselect
    _selectedTab = _myNapkinsTab!;
    _tabContentHolder?.clearAppend(_selectedTab?.root);

    autoUnsubscribe(
      host.onPageCreated.listen((e) {
        _allRecentTab?.invalidateCache();
        _teamSpaceTab?.invalidateCache();
        _myNapkinsTab?.invalidateCache();
        loadData(visibleTabOnly: false);
      }),
    );

    // this showing by default
    if (showByDefault) {
      open();
    } else {
      close();
    }

    autoUnsubscribe(context.auth.loggedInChanged.listen(_onLoggedInChanged));
    autoUnsubscribe(Environment.onResize.listen((_) => updateOverlapping()));
    updateOverlapping();
  }

  void release() {
    releaseSubscriptions();

    _selectedTab?.release();
    _selectedTab = null;
    _allRecentTab?.release();
    _allRecentTab = null;
    _myNapkinsTab?.release();
    _myNapkinsTab = null;
    _teamSpaceTab?.release();
    _teamSpaceTab = null;
  }

  void updateOverlapping() {
    _overlapping = Environment.window.innerWidth < overlappingThreshold;
    cachedRoot?.classList.toggle('overlapping', _overlapping);
  }

  // Handle workspaceId query parameter if present
  Future<void> _handleActiveWorkspaceFromQueryParameter() async {
    final workspaceActive = Flags().variation('workspace-editor', false);
    final urlWorkspaceId = browser.getUrlQueryParameter('joinWorkspaceId');
    if (urlWorkspaceId != null && urlWorkspaceId.isNotEmpty && workspaceActive) {
      final relevantWorkspace = context.workSpaceManager.workspaces.firstWhereOrNull((ws) => ws.id == urlWorkspaceId);
      if (relevantWorkspace != null) {
        _log.info(() => 'Setting active workspace from URL parameter: $urlWorkspaceId');
        await context.userConfig.setActiveWorkspaceId(urlWorkspaceId);
        await WorkspaceWelcomeDialog(relevantWorkspace.displayName).show();
      } else {
        _log.warning(() => 'Workspace ID from URL parameter not found in available workspaces: $urlWorkspaceId');
      }
      // Remove the query parameter from URL after processing
      browser.removeUrlQueryParameters(['joinWorkspaceId']);
    }
  }


  void _onWorkspacesUpdated() async {
    final workspaceActive = Flags().variation('workspace-editor', false);
    final shouldShow = workspaceActive && context.workSpaceManager.showTeamSpaceTab;
    final workspaces = context.workSpaceManager.workspaces;

    if (workspaces.isEmpty) return;
    await _handleActiveWorkspaceFromQueryParameter();

    // Fix selected workspace if changed/unavailable
    if (workspaceActive) {
      var activeId = await context.userConfig.getActiveWorkspaceId();
      if (!workspaces.any((ws) => ws.id == activeId)) {
        activeId = workspaces.first.id;
      }

      final selectedWorkspace = workspaces.any((ws) => ws.id == activeId) ? activeId : null;
      if (selectedWorkspace == null || activeId == null) {
        _log.severe(() => 'We are making sure we have an active workspace, but none is available!');
        return;
      }
      await context.userConfig.setActiveWorkspaceId(activeId);
    }

    final wasSelected = _selectedTab == _teamSpaceTab;

    if (shouldShow) {
      _teamSpaceTab = LibraryTab(this, 'Team Space', LibraryCollectionType.teamSpace);

      final workspaces = context.workSpaceManager.workspaces;
      final activeId = await context.userConfig.getActiveWorkspaceId();
      final selectedWorkspace = workspaces.any((ws) => ws.id == activeId) ? activeId : null;

      if (workspaces.length > 1) {
        _teamSpaceSelector = WorkspaceDropdown(
          context: context,
          workspaces: workspaces,
          selectedWorkspace: selectedWorkspace,
          onWorkspaceSelected: _onWorkspaceSelect,
          forLibrary: true,
        ).root;
      } else {
        _teamSpaceSelector = null;
      }
      _buildSubscriptions.addNotNull(_teamSpaceTab?.title.onClick.listen((e) => _selectTab(_teamSpaceTab!)));
    } else {
      _teamSpaceTab?.release();
      if (wasSelected) {
        _selectTab(_myNapkinsTab!);
      }
      _teamSpaceTab = null;
    }
    rebuild();
    // loadData(visibleTabOnly: false);
  }

  void _onWorkspaceSelect(String workspaceId, CxDropdownItemChangeSource _) async {
    await context.userConfig.setActiveWorkspaceId(workspaceId);
    loadData(visibleTabOnly: false, clearCache: true);
    await _loadFirstAvailablePage();
  }

  /// Loads the first available page following the same logic as PageView._enteringPage
  /// Tries teamSpace first (since we're switching workspaces)
  Future<void> _loadFirstAvailablePage() async {
    try {
      final pageModel = await PageLoaderUtils.loadFirstAvailablePage(context);

      if (pageModel != null) {
        await host.goToPage(pageModel.pageId, parameters: {});
      } else {
        // Last resort: create a new page if user can create pages
        final auth = context.auth;
        if (supportPageCreation && auth.loggedIn) {
          await host.goToCreatePage();
        }
      }
    } catch (e) {
      _log.warning(() => 'Failed to load first available page after workspace change: $e');
    }
  }

  // build
  // -----

  static Element buildLibraryPanelButton(Callback onClick, {required Function autoUnsubscribe}) {
    final button = HTMLDivElement()
      ..classList.addAll(['library_panel_button', 'button'])
      ..textContent = 'Library';
    autoUnsubscribe(button.onClick.listen((e) => onClick()));
    return button;
  }

  void _cancelBuildSubscriptions() {
    for (final sub in _buildSubscriptions) {
      sub.cancel();
    }
    _buildSubscriptions.clear();
  }

  @override
  HTMLElement build() {
    _cancelBuildSubscriptions();

    final libraryPanelButton = buildLibraryPanelButton(() => close(), autoUnsubscribe: autoUnsubscribe);
    final topButtons = HTMLDivElement()
      ..classList.add('top-buttons')
      ..appendChild(libraryPanelButton)
      ..appendNotNull(_addPageElement?.root);

    final closeButton = HTMLDivElement()
      ..classList.addAll(['close'])
      ..title = 'Close';
    _buildSubscriptions.add(closeButton.onClick.listen((e) => close()));

    _buildSubscriptions.addNotNull(_allRecentTab?.title.onClick.listen((e) => _selectTab(_allRecentTab!)));
    _buildSubscriptions.addNotNull(_myNapkinsTab?.title.onClick.listen((e) => _selectTab(_myNapkinsTab!)));
    _buildSubscriptions.addNotNull(_teamSpaceTab?.title.onClick.listen((e) => _selectTab(_teamSpaceTab!)));

    final tabTitles = HTMLDivElement()
      ..classList.add('tab-titles')
      ..appendChild(closeButton)
      ..appendNotNull(_myNapkinsTab?.title)
      ..appendNotNull(_teamSpaceTab?.title)
      ..appendNotNull(_allRecentTab?.title);

    _tabContentHolder = HTMLDivElement()
      ..classList.add('tab-content-holder')
      ..appendNotNull(_selectedTab?.root);

    _selectedTab?.title.classList.toggle('selected', true);

    final divider = HTMLDivElement()..classList.add('divider');

    _libraryElement
      ..classList.addAll([if (!supportPageCreation) 'to_desktop', if (loadingFuture != null) 'loading'])
      ..clear() // To fix rebuild issues, where element was not getting cleared
      ..appendChild(topButtons)
      ..appendNotNull(supportPageCreation ? null : AddPageElement.buildCreateOnDesktop())
      ..appendChild(tabTitles)
      ..appendChild(HTMLDivElement()..classList.add('tab-before-items'))
      ..appendNotNull(
        _teamSpaceSelector == null ? null : HTMLDivElement()
          ?..classList.add('library-teamspace-selector')
          ..appendChild(_teamSpaceSelector!)
          ..appendChild(divider),
      )
      ..appendChild(_tabContentHolder!);

    final closeArea = HTMLDivElement()..classList.add('close-area');
    _buildSubscriptions.add(closeArea.onClick.listen((e) => close()));

    // dom_capture_all is to avoid sending the event the graph that can be underneath
    return HTMLDivElement()
      ..classList.addAll(['library-panel', 'left-panel', 'dom_capture_all', if (_isShowing) 'showing', if (_overlapping) 'overlapping'])
      ..appendChild(_libraryElement)
      ..appendChild(closeArea);
  }

  void open() {
    if (isShowing) return;
    cachedRoot?.classList.add('showing');
    _isShowing = true;
    _onOpen();
    _onIsShowingChangedController.addIfNotClosed(_isShowing);

    Analytics().trackEvents(
      AnalyticsEvents(
        context: AnalyticsContextEvent(ContextEventName.library_open),
        monitoring: AnalyticsEvent('LIBRARY/OPEN'),
        tagManager: AnalyticsEvent('napkin_library_open'),
      ),
    );
  }

  void close() {
    if (!isShowing) return;
    cachedRoot?.classList.remove('showing');
    _isShowing = false;
    _onClose();
    _onIsShowingChangedController.addIfNotClosed(_isShowing);

    Analytics().trackEvents(
      AnalyticsEvents(
        context: AnalyticsContextEvent(ContextEventName.library_close),
        monitoring: AnalyticsEvent('LIBRARY/CLOSE'),
        tagManager: AnalyticsEvent('napkin_library_close'),
      ),
    );
  }

  void _onOpen() {
    loadData(visibleTabOnly: false);

    cachedRoot?.classList.add('loading');
    loadingFuture?.then((_) {
      cachedRoot?.classList.remove('loading');
      final sum = pageCount;
      _log.info(() => 'Library loaded ${sum} pages');
      if (sum == 0 && _isShowing) libraryIsEmpty();
    });
  }

  void _onClose() {}

  int get pageCount => (_myNapkinsTab?.pageCount ?? 0) + (_teamSpaceTab?.pageCount ?? 0) + (_allRecentTab?.pageCount ?? 0);

  void libraryIsEmpty() {
    root.classList.add('is_empty');
  }

  @override
  Future<PageModel?> goToPage(PageId? pageId, {required ParametersMap parameters}) async {
    Analytics().trackEvents(
      AnalyticsEvents(
        context: AnalyticsContextEvent(ContextEventName.library_open_page),
        monitoring: AnalyticsEvent('LIBRARY/GO_TO_PAGE'),
        tagManager: AnalyticsEvent('napkin_library_go_to_page'),
      ),
      pageId: pageId?.id,
    );

    return host.goToPage(pageId, parameters: parameters);
  }

  @override
  Future<void> onPageDeleted(String pageId, LibraryTab libraryTab, int removedAt) async {
    if (loadingFuture != null) await loadingFuture; // in case we are currently reloading

    // pages may be duplicated in the library
    _myNapkinsTab?.removeDeletedPage(pageId);
    _teamSpaceTab?.removeDeletedPage(pageId);
    _allRecentTab?.removeDeletedPage(pageId);

    // Go to a neighbor page if possible
    final nextIndex = removedAt.clamp(0, max<int>(0, libraryTab.pageCount - 1));
    final nextId = libraryTab.pageIdAt(nextIndex) ?? _myNapkinsTab?.pageIdAt(0) ?? _teamSpaceTab?.pageIdAt(0) ?? _allRecentTab?.pageIdAt(0);

    if (nextId != null) {
      host.goToPage(nextId, parameters: {});
    } else if (supportPageCreation) {
      host.goToCreatePage();
    } else {
      libraryIsEmpty();
    }

    Analytics().trackEvents(
      AnalyticsEvents(
        context: AnalyticsContextEvent(ContextEventName.library_delete_page),
        monitoring: AnalyticsEvent('LIBRARY/DELETE_PAGE'),
        tagManager: AnalyticsEvent('napkin_library_delete_page'),
      ),
      pageId: pageId,
    );
  }

  @override
  Future<void> onPageDuplicated(String pageId, String pageTitle) async {
    await host.onPageDuplicated(pageId, pageTitle);
  }

  void _selectTab(LibraryTab newSelectedTab) {
    if (_selectedTab == newSelectedTab) return;
    _selectedTab?.onHide();
    _selectedTab = newSelectedTab;
    _selectedTab?.onShow();
    _tabContentHolder?.clearAppend(_selectedTab?.root);

    Analytics().trackEvents(
      AnalyticsEvents(
        context: AnalyticsContextEvent(ContextEventName.library_tab_change, parameters: {'tab': '${_selectedTab?.title.textContent}'}),
        monitoring: AnalyticsEvent('LIBRARY/TAB_CHANGE', {'tab': '${_selectedTab?.title.textContent}'}),
        tagManager: AnalyticsEvent('napkin_library_tab_change', {'tab': '${_selectedTab?.title.textContent}'}),
      ),
    );
  }

  // Will load all pages for the tab that is not selected
  Future loadData({bool visibleTabOnly = false, bool? clearCache}) async {
    _isLoadingDataCompleter = Completer();

    if (!visibleTabOnly || _selectedTab?.name == _myNapkinsTab?.name) {
      await _myNapkinsTab?.reload(clearCache: clearCache);

      if (_selectedTab?.name == _myNapkinsTab?.name) {
        _isLoadingDataCompleter?.complete();
        _isLoadingDataCompleter = null;
      }

      Analytics().trackEvents(
        AnalyticsEvents(
          context: AnalyticsContextEvent(ContextEventName.library_load_own_pages, parameters: {'count': _myNapkinsTab?.pageCount}, extractDataOnly: true),
        ),
      );
    }

    if (!visibleTabOnly || _selectedTab?.name == _allRecentTab?.name) {
      await _allRecentTab?.reload(clearCache: clearCache);
      if (_selectedTab?.name == _allRecentTab?.name) {
        _isLoadingDataCompleter?.complete();
        _isLoadingDataCompleter = null;
      }
    }
    if (!visibleTabOnly || _selectedTab?.name == _teamSpaceTab?.name) {
      await _teamSpaceTab?.reload(clearCache: clearCache);
      if (_selectedTab?.name == _teamSpaceTab?.name) {
        _isLoadingDataCompleter?.complete();
        _isLoadingDataCompleter = null;
      }
    }
    if (currentPageModel != null && context.auth.user != null && !currentPageModel!.isOwner(context.auth.user!)) {
      _selectTab(_allRecentTab!);
      _isLoadingDataCompleter?.complete();
      _isLoadingDataCompleter = null;
    }
  }

  @override
  PageModel? get currentPageModel => host.currentPageModel;

  void markSelected(String pageId, {required bool editable}) {
    _myNapkinsTab?.markItemSelected(pageId, editable: editable);
    _teamSpaceTab?.markItemSelected(pageId, editable: editable);
    _allRecentTab?.markItemSelected(pageId, editable: editable);
  }

  void _onLoggedInChanged((bool, dynamic) data) {
    final loggedIn = data.$1;
    if (!loggedIn) {
      close();
      // Deep cleanup
      _myNapkinsTab?.clear();
      _teamSpaceTab?.clear();
      _allRecentTab?.clear();
    } else {
      loadData(visibleTabOnly: false, clearCache: true);
    }
  }

  @override
  void downloadAsPdf({String? id, String? title}) => host.downloadAsPdf(id: id, title: title);

  void toggleAddPageButton({required bool visible}) {
    _addPageElement?.toggle(visible: visible);
  }
}

class LibraryPanelPrivateer {
  final LibraryPanel _libraryPanel;
  LibraryPanelPrivateer._(this._libraryPanel);

  void selectTab(LibraryTab newSelectedTab) => _libraryPanel._selectTab(newSelectedTab);
  LibraryTab get selectedTab => _libraryPanel._selectedTab!;
}
