import 'dart:async';
import 'dart:convert';
import 'dart:typed_data';

import 'package:ai_web_lib/ai_web_lib.dart';
import 'package:analytics_web_lib/analytics_web_lib.dart';
import 'package:font_web_lib/font_web_lib.dart';
import 'package:io_web_lib/logging.dart';
import 'package:util_web_lib/custom_svg_font.dart';
import 'package:util_web_lib/dialog.dart';
import 'package:util_web_lib/environment.dart';
import 'package:util_web_lib/function.dart';
import 'package:util_web_lib/has_subscription.dart';
import 'package:util_web_lib/html_root.dart';
import 'package:util_web_lib/js_interop.dart';
import 'package:util_web_lib/loader.dart';
import 'package:util_web_lib/tooltip_element.dart';

import '../../app/context.dart';
import '../page/app_feature_enums.dart';
import '../page/font/font_repository.dart';
import '../page/orchestrator/feature_disabler.dart';

class AddCustomFontDialog with HtmlRoot, HasSubscription {
  static final Logger _log = Logger('AddCustomFontDialog', browser: Logger.INFO);

  bool _released = false;
  Dialog? _dialog;

  List<CustomFontType> _importTabs;
  CustomFontType? _currentTab;
  final bool _uploadDisabled;

  TooltipElement? _tooltip;

  _GoogleFontsTab? _googleFontsOption;
  _UploadTab? _uploadOption;

  final Map<CustomFontType, _CustomFontType> _tabs = {};

  final Context context;

  final FontsRepository _fontsRepository;

  final AddFontsCallback _addFontsCallback;
  final Callback _onCloseCallback;

  static AddCustomFontDialog? _singleton;
  factory AddCustomFontDialog({
    required Context context,
    required FontsRepository fontsRepository,
    required AddFontsCallback addFontsCallback,
    required Callback onCloseCallback,
  }) {
    // Make sure we only have one instance of AddCustomFontModal
    if (_singleton != null) {
      _singleton?.release();
      _singleton = null;
    }

    _singleton = AddCustomFontDialog._(context, fontsRepository, addFontsCallback, onCloseCallback);
    return _singleton!;
  }

  AddCustomFontDialog._(this.context, this._fontsRepository, this._addFontsCallback, this._onCloseCallback)
    : _importTabs = [],
      _uploadDisabled = FeatureDisabler().isFeatureDisabled(AppFeatureEnum.uploadFonts) {
    _importTabs.add(CustomFontType.googleFonts);
    _importTabs.add(CustomFontType.upload);
    _currentTab = _importTabs.firstOrNull;
  }

  void release() {
    if (_released) return;
    _released = true;

    _currentTab = null;

    for (final option in _tabs.values) {
      option.release();
    }
    _tabs.clear();

    _dialog?.close();
    _dialog = null;

    _tooltip?.release();
    _tooltip = null;

    releaseSubscriptions();

    _singleton = null;
  }

  void open() async {
    if (_importTabs.isEmpty) {
      _log.severe(() => 'No import options available');
      return;
    }

    _dialog = Dialog.custom([root], closeButton: true, classes: ['add-custom-font-dialog'], onClose: close)..show();

    sendAnalyticsEvent('modal', 'open');
  }

  void close([_]) {
    sendAnalyticsEvent('modal', 'close');
    release();
    _onCloseCallback();
  }

  static void sendAnalyticsEvent(String name, String action, {JsonMap data = const {}}) {
    final analyticsParams = {'name': name, 'action': action, ...data};
    Analytics().trackEvents(
      AnalyticsEvents(
        monitoring: AnalyticsEvent('ADD_CUSTOM_FONT_DIALOG/${name.toUpperCase()}/${action.toUpperCase()}', analyticsParams),
        tagManager: AnalyticsEvent('napkin_custom_font_modal_action', GoogleTagManager.buildPayload(eventParams: analyticsParams)),
      ),
    );
  }

  static List<JsonMap> customFontsToAnalyticsObject(Iterable<CustomFont> fonts) {
    return fonts
        .map(
          (e) => {
            'name': e.name,
            'type': e.type.name,
            'styles': (e.styles.map((s) => {'name': s.name, 'weight': s.weight, 'style': s.style})).toList(),
          },
        )
        .toList();
  }

  @override
  HTMLElement build() {
    return HTMLDivElement()
      ..classList.add('add-custom-font-dialog-container')
      ..appendAll([_buildHeader(), _buildContent()]);
  }

  HTMLElement _buildHeader() {
    return HTMLDivElement()
      ..classList.add('add-custom-font-dialog-header')
      ..appendAll([
        if (_importTabs.length > 1) ...[
          HTMLParagraphElement()
            ..textContent = 'Add a new font from...'
            ..classList.add('add-custom-font-dialog-title'),
          _buildHeaderMenu(),
        ] else
          HTMLParagraphElement()
            ..textContent = 'Add a new font'
            ..classList.add('add-custom-font-dialog-title'),
      ]);
  }

  HTMLElement _buildHeaderMenu() {
    final googleFontsButton = HTMLButtonElement()
      ..classList.toggle('active', _currentTab == CustomFontType.googleFonts)
      ..textContent = 'Google Fonts';
    autoUnsubscribe(googleFontsButton.onClick.listen(_onGoogleFontsClicked));

    final uploadButton = HTMLButtonElement()
      ..classList.toggle('active', _currentTab == CustomFontType.upload)
      ..textContent = 'Upload';
    if (!_uploadDisabled) autoUnsubscribe(uploadButton.onClick.listen(_onUploadClicked));

    if (_uploadDisabled) {
      final content = HTMLDivElement()..innerHTML='Available in<br/><span class="cx-icon plus-tag translucent" style="display:contents"></span> and <span class="cx-icon pro-tag translucent" style="display:contents; height: 14px; width: 38px;"></span>'.toJS;
      TooltipElement.withAutoHover(
          targetElement: uploadButton,
          placement: PointyPlacement.bottomCenter,
          htmlContent: content,
          innerClasses: ['tooltip-upgrade-nudge']
      );
    }

    return HTMLDivElement()
      ..classList.add('add-custom-font-dialog-header-menu')
      ..appendAll([googleFontsButton, uploadButton]);
  }

  void _onGoogleFontsClicked(_) {
    sendAnalyticsEvent('tabs', 'click', data: {'tab': 'googleFonts'});
    _changeCurrentOption(CustomFontType.googleFonts);
  }

  void _onUploadClicked(_) {
    if (_uploadDisabled) return;
    sendAnalyticsEvent('tabs', 'click', data: {'tab': 'upload'});
    _changeCurrentOption(CustomFontType.upload);
  }

  void _changeCurrentOption(CustomFontType newOption) {
    _currentTab = newOption;
    rebuild();
  }

  Future<void> _onFontSelected(Iterable<CustomFont> fonts) async {
    if (fonts.isEmpty) return;

    final loadingMessage = fonts.length > 1 ? 'Adding ${fonts.length} fonts...' : 'Adding ${fonts.first.name} font...';
    final waitDialog = Dialog.wait(textContent: loadingMessage);

    sendAnalyticsEvent('font', 'import', data: {'font': jsonEncode(customFontsToAnalyticsObject(fonts))});

    await CustomFontsManager().loadFonts(fonts);
    waitDialog.close();
    _addFontsCallback(fonts);
  }

  HTMLElement _buildContent() {
    final contentRoot = HTMLDivElement()..classList.add('add-custom-font-dialog-content');

    final currentOptionType = _currentTab;
    if (currentOptionType == null) {
      return contentRoot;
    }

    final currentOption = _tabs[currentOptionType];
    if (currentOption != null) {
      contentRoot.appendChild(currentOption.build());
      return contentRoot;
    }

    switch (currentOptionType) {
      case CustomFontType.googleFonts:
        {
          _googleFontsOption ??= _GoogleFontsTab(_fontsRepository, _onFontSelected);
          _tabs[currentOptionType] = _googleFontsOption!;
        }
      case CustomFontType.upload:
        {
          _uploadOption ??= _UploadTab(context, _fontsRepository, _onFontSelected);
          _tabs[currentOptionType] = _uploadOption!;
        }
    }

    contentRoot.appendChild(_tabs[currentOptionType]!.build());
    return contentRoot;
  }
}

abstract class _CustomFontType {
  void release();

  HTMLElement build();
  HTMLElement get root;
}

class _GoogleFontsTab with HtmlRoot, HasSubscription implements _CustomFontType {
  static final Logger _log = Logger('_GoogleFontsTab', browser: Logger.INFO);

  final FontsRepository _fontsRepository;

  GoogleFontsList? _googleFontsList;
  AddFontsCallback? _addFontsCallback;

  _GoogleFontsTab(this._fontsRepository, this._addFontsCallback) {
    _googleFontsList = GoogleFontsList(_onFontSelected, []);
    _init();
  }

  @override
  void release() {
    releaseSubscriptions();

    _googleFontsList?.release();
    _googleFontsList = null;
    _addFontsCallback = null;
  }

  Future<void> _init() async {
    final installedFonts = await _fontsRepository.getAllCustomFonts(type: CustomFontType.googleFonts);
    _googleFontsList?.addInstalledFonts(installedFonts.map((e) => GoogleFont.fromCustomFont(e)).toList());
  }

  @override
  HTMLElement build() {
    return _googleFontsList?.root ?? HTMLDivElement();
  }

  void _onFontSelected(GoogleFont font) async {
    final customFont = await _fontsRepository.getOrCreateFontFamily(
      type: CustomFontType.googleFonts,
      key: font.key,
      familyName: font.name,
      category: font.category,
    );
    if (customFont == null) {
      _log.warning(() => 'Failed to create font: ${font.name}');
      return;
    }

    customFont.addStyles(font.styles);
    await _fontsRepository.saveFont(customFont);

    _addFontsCallback?.call([customFont]);
  }
}

enum _UploadTabStep { upload, loading, review }

class _UploadTab with HtmlRoot, HasSubscription implements _CustomFontType {
  static final Logger _log = Logger('_UploadOption', browser: Logger.INFO);

  // Error messages
  static const String _errorMessageInvalidFont = 'Invalid font file. Please only select .ttf files';
  static const String _errorMessageFailedToUpload = 'Failed to upload font file. Please try again';

  final Context context;
  final FontsRepository _fontsRepository;
  final AddFontsCallback _addFontsCallback;

  HTMLDivElement? _contentContainer;
  HTMLDivElement? _dragDropContainer;

  String? _errorMessage;
  HTMLInputElement? _browseFilesInput;

  UploadFontsManager? _uploadFontsManager;
  UploadFontsManager get uploadFontsManager => _uploadFontsManager ??= UploadFontsManager();

  _UploadTabStep _state = _UploadTabStep.upload;
  final Map<String, List<_FontFileHolder>> _fontFiles = {};

  _UploadTab(this.context, this._fontsRepository, this._addFontsCallback);

  @override
  void release() {
    releaseSubscriptions();

    _browseFilesInput = null;
    _uploadFontsManager?.release();
    _uploadFontsManager = null;

    _fontFiles.clear();
  }

  @override
  HTMLElement build() {
    return HTMLDivElement()
      ..classList.add('upload-option')
      ..appendChild(_buildContent());
  }

  HTMLElement _buildContent() {
    _contentContainer ??= HTMLDivElement()..classList.add('upload-option-content');
    _contentContainer?.clear();

    switch (_state) {
      case _UploadTabStep.upload:
        _contentContainer!.appendChild(_buildContentUploadStep());
        break;
      case _UploadTabStep.loading:
        _contentContainer!.appendChild(Loader().root);
        break;
      case _UploadTabStep.review:
        _contentContainer!.appendChild(_buildContentReviewStep());
        break;
    }

    return _contentContainer!;
  }

  HTMLElement? _buildErrorMessage() {
    final error = _errorMessage;
    if (error == null) return null;

    return HTMLDivElement()
      ..classList.addAll(['upload-option-message-banner', 'upload-option-message-banner-error'])
      ..textContent = error;
  }

  // ****************
  // Upload step
  // ****************

  HTMLElement _buildContentUploadStep() {
    cancelSubscriptions('_buildContentUploadStep');

    final container = HTMLDivElement()..classList.add('upload-option-content-upload');

    // Top banner
    container.appendChild(
      HTMLDivElement()
        ..classList.add('upload-option-message-banner')
        ..appendChild(HTMLParagraphElement()..textContent = 'Napkin only supports .ttf files currently'),
    );

    container
      ..appendNotNull(_buildErrorMessage())
      ..appendChild(_dragDropContainer ??= _buildDropArea())
      ..appendChild(_browseFilesInput ??= _buildBrowseFilesInput());

    // If there are uploaded fonts, show the continue button
    if (_fontFiles.isNotEmpty) {
      final goBackButton = HTMLButtonElement()
        ..classList.addAll(['button', 'text_button', 'text_button_secondary'])
        ..textContent = 'Go back';
      autoUnsubscribe(goBackButton.onClick.listen(_onContinueToReviewClicked), '_buildContentUploadStep');

      container.appendChild(
        HTMLDivElement()
          ..classList.add('upload-option-content-footer')
          ..appendAll([goBackButton]),
      );
    }

    return container;
  }

  HTMLDivElement _buildDropArea() {
    final dropArea = HTMLDivElement()
      ..classList.add('upload-option-content-drag-drop')
      ..appendAll([
        HTMLParagraphElement()
          ..classList.add('drop_area_text')
          ..appendAll([
            HTMLAnchorElement()
              ..href = '#'
              ..textContent = 'browse',
            HTMLBRElement(),
            HTMLParagraphElement()..textContent = 'or drag your .ttf files here.',
          ]),
      ]);

    autoUnsubscribeBuild(dropArea.onDragEnter.listen(_onDragOver));
    autoUnsubscribeBuild(dropArea.onDragOver.listen(_onDragOver));
    autoUnsubscribeBuild(dropArea.onDragLeave.listen(_onDragEnd));
    autoUnsubscribeBuild(dropArea.onDrop.listen(_onFilesDrop));
    autoUnsubscribeBuild(dropArea.onClick.listen(_onBrowseFilesClicked));

    return dropArea;
  }

  HTMLInputElement _buildBrowseFilesInput() {
    final browseFilesInput = HTMLInputElement()
      ..type = 'file'
      ..accept = '.ttf'
      ..multiple = true
      ..style.display = 'none';

    autoUnsubscribe(browseFilesInput.onChange.listen(_onFilesSelected));

    return browseFilesInput;
  }

  void _onContinueToReviewClicked(_) {
    _state = _UploadTabStep.review;
    _buildContent();
  }

  void _onBrowseFilesClicked(_) {
    _browseFilesInput?.click();
  }

  void _onDragOver(Event? event) {
    event?.preventDefault();
    event?.stopPropagation();

    _dragDropContainer?.classList.add('drag-hover');
  }

  void _onDragEnd(Event? event) {
    event?.preventDefault();
    event?.stopPropagation();

    _dragDropContainer?.classList.remove('drag-hover');
  }

  void _onFilesDrop(Event? event) {
    _dragDropContainer?.classList.remove('drag-hover');

    if (event == null) return;

    event.preventDefault();
    event.stopPropagation();

    try {
      if (!event.isA<DragEvent>()) return;

      final dataTransfer = (event as DragEvent).dataTransfer;
      if (dataTransfer == null) {
        return;
      }

      final files = dataTransfer.files;
      if (files.length == 0) {
        return;
      }

      _processFilesUpload(files.toList());

      AddCustomFontDialog.sendAnalyticsEvent('upload', 'drop', data: {'files': files.length});
    } catch (e) {
      _log.severe(() => 'Failed to read dropped files: $e');
      return;
    }
  }

  void _onFilesSelected(_) async {
    final files = _browseFilesInput?.files;
    if (files == null || files.length == 0) {
      _errorMessage = _errorMessageInvalidFont;
      _state = _UploadTabStep.upload;
      _buildContent();
      return;
    }

    _processFilesUpload(files.toList());

    AddCustomFontDialog.sendAnalyticsEvent('upload', 'browse', data: {'files': files.length});
  }

  void _processFilesUpload(Iterable<File> files) async {
    _state = _UploadTabStep.loading;
    _buildContent();

    final loadingFutures = <Future>[];
    for (final file in files.toList()) {
      _log.info(() => 'Selected file: ${file.name}');

      try {
        final completer = Completer();
        final reader = FileReader();
        reader.readAsArrayBuffer(file);

        reader.onLoadEnd.listen((_) {
          try {
            final fontBytes = Uint8List.view(reader.result as ByteBuffer);
            if (fontBytes.isEmpty) {
              _log.severe(() => 'Failed to read font file: Empty file');
              completer.tryComplete();
              return;
            }

            final font = uploadFontsManager.parseFont(fontBytes);
            if (!font.supported) {
              _log.severe(() => 'Failed to read font file: Unsupported font');
              completer.tryComplete();
              return;
            }

            final fontFamilyName = font.getPreferredFontFamily() ?? font.getFontFamily();
            final fontSubfamilyName = font.getPreferredFontSubfamily() ?? font.getFontSubfamily();
            final fontFullName = font.getFontFullName();
            final fontWeight = font.getFontWeight();
            final fontStyle = font.getFontStyle();

            if (fontFamilyName == null ||
                fontFamilyName.isEmpty ||
                fontSubfamilyName == null ||
                fontSubfamilyName.isEmpty ||
                fontFullName == null ||
                fontFullName.isEmpty ||
                fontWeight == null) {
              _log.severe(
                () =>
                    'Failed to read font information (${file.name}): fontFamilyName: $fontFamilyName - fontSubfamilyName: $fontSubfamilyName - fontFullName: $fontFullName - fontWeight: $fontWeight',
              );

              // Enable this line to inspect the OpenType font object
              // Environment.jsContext.setProperty('debug_font', font);

              completer.tryComplete();
              return;
            }

            if (_fontFiles[fontFamilyName]?.any((e) => e.fileName == file.name) == true) {
              _log.warning(() => 'Font file already uploaded: ${file.name}');
              completer.tryComplete();
              return;
            }

            _fontFiles.putIfAbsent(fontFamilyName, () => []);
            _fontFiles[fontFamilyName]?.add(
              _FontFileHolder(file.name, fontFullName, fontFamilyName, fontSubfamilyName, fontWeight.toString(), fontStyle, fontBytes),
            );
            completer.tryComplete();
          } catch (e) {
            _log.severe(() => 'Failed to read font file: $e');
            completer.tryComplete();
          }
        });

        loadingFutures.add(completer.future.timeout(const Duration(seconds: 10)));
      } catch (exception) {
        _log.severe(() => 'Failed to read file: $exception');
      }
    }

    try {
      await Future.wait(loadingFutures);

      if (_fontFiles.isEmpty) {
        _errorMessage = _errorMessageInvalidFont;
        _state = _UploadTabStep.upload;
      } else {
        _state = _UploadTabStep.review;
      }

      _buildContent();
    } catch (e) {
      _log.severe(() => 'Failed to load font files: $e');
    }
  }

  // ****************
  // Review step
  // ****************

  HTMLElement _buildContentReviewStep() {
    cancelSubscriptions('_buildContentReviewStep');

    final container = HTMLDivElement()..classList.add('upload-option-content-review');

    container.appendNotNull(_buildErrorMessage());
    container.appendChild(_buildContentReviewContent());

    final addFontsButton = HTMLButtonElement()
      ..classList.addAll(['button', 'text_button'])
      ..textContent = _fontFiles.length > 1 ? 'Add fonts' : 'Add font';
    autoUnsubscribe(addFontsButton.onClick.listen(_onAddFontsClicked), '_buildFooter');

    final addMoreFilesButton = HTMLButtonElement()
      ..classList.addAll(['button', 'text_button', 'text_button_secondary'])
      ..textContent = 'Add more files';
    autoUnsubscribe(addMoreFilesButton.onClick.listen(_onMoreFilesClicked), '_buildFooter');

    container.appendChild(
      HTMLDivElement()
        ..classList.add('upload-option-content-footer')
        ..appendAll([addMoreFilesButton, addFontsButton]),
    );

    return container;
  }

  HTMLElement _buildContentReviewContent() {
    cancelSubscriptions('_buildContentReviewContent');

    final container = HTMLDivElement()..classList.add('upload-option-content-review-container');

    for (final font in _fontFiles.values) {
      if (font.isEmpty) continue;

      final fontFamilySection = HTMLDivElement()..classList.add('upload-option-content-review-font-family');
      container.appendChild(fontFamilySection);

      fontFamilySection.appendChild(
        HTMLParagraphElement()
          ..textContent = font.first.fontFamily
          ..classList.add('upload-option-content-review-font-title')
          ..appendChild(HTMLSpanElement()..classList.addAll(['icon', 'font-family'])),
      );

      for (final fontFile in font) {
        final fontVariant = HTMLDivElement()..classList.add('upload-option-content-review-font-row');

        final fileName = HTMLParagraphElement()
          ..textContent = '${fontFile.styleName} (${fontFile.fileName})'
          ..classList.add('upload-option-content-review-font-file-name');
        fontVariant.appendChild(fileName);

        final removeButton = HTMLButtonElement()
          ..classList.addAll(['icon', 'delete', 'upload-option-content-review-font-remove'])
          ..textContent = 'Remove';
        autoUnsubscribe(removeButton.onClick.listen((_) => _onRemoveFontClicked(fontFile)), '_buildContentReviewContent');

        fontVariant.appendChild(removeButton);
        fontFamilySection.appendChild(fontVariant);
      }
    }

    return container;
  }

  void _onRemoveFontClicked(_FontFileHolder fontFile) {
    _fontFiles[fontFile.fontFamily]?.remove(fontFile);
    if (_fontFiles[fontFile.fontFamily]?.isEmpty ?? false) {
      _fontFiles.remove(fontFile.fontFamily);
    }

    if (_fontFiles.isEmpty) {
      _state = _UploadTabStep.upload;
    }

    _buildContent();

    AddCustomFontDialog.sendAnalyticsEvent('upload', 'remove', data: {'font': fontFile.fileName});
  }

  void _onMoreFilesClicked(_) {
    _state = _UploadTabStep.upload;
    _buildContent();

    AddCustomFontDialog.sendAnalyticsEvent('upload', 'add_more_files');
  }

  void _onAddFontsClicked(_) {
    _uploadFonts();

    AddCustomFontDialog.sendAnalyticsEvent('upload', 'add_fonts', data: {'fonts': _fontFiles.length});
  }

  void _uploadFonts() async {
    _state = _UploadTabStep.loading;
    _buildContent();

    final uploadedFonts = <CustomFont>[];
    final uploadedFontFiles = _fontFiles.values.toList().shallowCopy();
    for (final fontFiles in uploadedFontFiles) {
      if (fontFiles.isEmpty) continue;
      final customFont = await _fontsRepository.getOrCreateFontFamily(
        type: CustomFontType.upload,
        key: fontFiles.first.fontFamily,
        familyName: fontFiles.first.fontFamily,
        category: 'sans-serif',
      ); // We don't have category for uploaded fonts
      if (customFont == null) {
        _log.warning(() => 'Failed to create font: ${fontFiles.first.fontFamily}');
        continue;
      }

      for (final fontFile in fontFiles) {
        try {
          final fileKey = '${fontFile.fontFamily}_${fontFile.weight}';
          final url = await _fontsRepository.uploadFontFile(customFont.id, fileKey, fontFile.bytes);
          if (url != null) {
            final weight = fontFile.weight.toString();
            final style = fontFile.style.toString();
            customFont.addStyle(
              CustomFontStyle(
                fontFamilyDisplayName: customFont.name,
                fontFamilyCssName: customFont.name,
                fontFamilyType: CustomFontType.upload,
                name: fontFile.styleName,
                url: url,
                weight: weight,
                style: style,
                format: CustomFontFormat.ttf,
              ),
            );
          }
        } catch (e) {
          _log.warning(() => 'Failed to upload font file: ${fontFile.fileName}');
        }
      }

      final fontData = await _fontsRepository.saveFont(customFont);
      final savedFont = fontData.$2;
      if (savedFont == null) {
        _log.warning(() => 'Failed to save font: ${customFont.name}');
        continue;
      }
      uploadedFonts.add(savedFont);

      await CustomFontsManager().loadFont(customFont);
    }

    if (uploadedFonts.isEmpty) {
      _errorMessage = _errorMessageFailedToUpload;
      _state = _UploadTabStep.review;
      _buildContent();
      return;
    }

    _addFontsCallback(uploadedFonts);
  }
}

class _FontFileHolder {
  final String fontFamily; // Example: 'Open Sans'
  final String fontFullName; // Example: 'Open Sans Regular'
  final String fileName; // Example: 'OpenSans-Regular.ttf'
  final String styleName; // Example: 'Regular'

  final String weight; // Example: '400'
  final String style; // Example: 'normal'
  final Uint8List bytes;

  _FontFileHolder(this.fileName, this.fontFullName, this.fontFamily, this.styleName, this.weight, this.style, this.bytes);
}
